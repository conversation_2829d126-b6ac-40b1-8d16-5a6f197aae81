import React, { useEffect } from 'react';
import { Card, Table, Tag, Tooltip, Typography } from 'antd';
import { ExclamationCircleOutlined, WarningOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Text } = Typography;

interface LogTableProps {
  data: API.IOperationLogDetail[];
  total: number;
  currentPage: number;
  pageSize: number;
  loading?: boolean;
  onPageChange: (page: number, size?: number) => void;
  onRowClick: (record: API.IOperationLogDetail) => void;
}

/**
 * 日志列表表格组件
 */
const LogTable: React.FC<LogTableProps> = ({
  data,
  total,
  currentPage,
  pageSize,
  loading = false,
  onPageChange,
  onRowClick,
}) => {
  // 获取日志级别标签
  const getLevelTag = (level?: string) => {
    switch (level) {
      case 'error':
        return (
          <Tag color="error" icon={<ExclamationCircleOutlined />}>
            错误
          </Tag>
        );
      case 'warn':
        return (
          <Tag color="warning" icon={<WarningOutlined />}>
            警告
          </Tag>
        );
      case 'info':
      default:
        return (
          <Tag color="success" icon={<InfoCircleOutlined />}>
            信息
          </Tag>
        );
    }
  };

  // 获取操作类型显示文本
  const getOperationTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      questionnaire_create: '问卷创建',
      questionnaire_update: '问卷编辑',
      questionnaire_delete: '问卷删除',
      response_submit: '评价提交',
      statistics_query: '统计查询',
      user_login: '用户登录',
      user_logout: '用户登出',
    };
    return typeMap[type] || type;
  };

  // 截取日志内容摘要
  const getLogSummary = (description: string, maxLength = 50) => {
    if (!description) return '-';
    if (description.length <= maxLength) return description;
    return description.substring(0, maxLength) + '...';
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) return;
      
      switch (event.key) {
        case 'ArrowLeft':
          if (currentPage > 1) {
            onPageChange(currentPage - 1);
          }
          break;
        case 'ArrowRight':
          if (currentPage < Math.ceil(total / pageSize)) {
            onPageChange(currentPage + 1);
          }
          break;
        case 'Home':
          if (currentPage !== 1) {
            onPageChange(1);
          }
          break;
        case 'End':
          const lastPage = Math.ceil(total / pageSize);
          if (currentPage !== lastPage) {
            onPageChange(lastPage);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentPage, total, pageSize, onPageChange]);

  // 表格列定义
  const columns: ColumnsType<API.IOperationLogDetail> = [
    {
      title: '日志时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      sorter: true,
      defaultSortOrder: 'descend',
      render: (time: string) => (
        <Text style={{ fontSize: '12px' }}>
          {dayjs(time).format('YYYY-MM-DD HH:mm:ss')}
        </Text>
      ),
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      align: 'center',
      filters: [
        { text: '信息', value: 'info' },
        { text: '警告', value: 'warn' },
        { text: '错误', value: 'error' },
      ],
      render: (level: string) => getLevelTag(level),
    },
    {
      title: '学校名称',
      dataIndex: 'school_name',
      key: 'school_name',
      width: 150,
      ellipsis: true,
      render: (name: string) => name || '-',
    },
    {
      title: '操作类型',
      dataIndex: 'operation_type',
      key: 'operation_type',
      width: 120,
      render: (type: string) => (
        <Tag color="blue">{getOperationTypeText(type)}</Tag>
      ),
    },
    {
      title: '日志内容',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description: string) => (
        <Tooltip title={description} placement="topLeft">
          <Text>{getLogSummary(description)}</Text>
        </Tooltip>
      ),
    },
    {
      title: '用户',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 100,
      ellipsis: true,
      render: (name: string) => name || '-',
    },
    {
      title: '响应时间',
      dataIndex: 'response_time',
      key: 'response_time',
      width: 100,
      align: 'right',
      render: (time: number) => (
        <Text style={{ fontSize: '12px' }}>
          {time ? `${time}ms` : '-'}
        </Text>
      ),
    },
  ];

  return (
    <Card title="日志列表">
      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="id"
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: onPageChange,
          onShowSizeChange: onPageChange,
        }}
        scroll={{ x: 1000 }}
        size="small"
        onRow={(record) => ({
          style: { 
            cursor: 'pointer',
            backgroundColor: record.level === 'error' ? '#fff2f0' : undefined,
          },
          onClick: () => onRowClick(record),
          onDoubleClick: () => onRowClick(record),
        })}
        rowClassName={(record) => 
          record.level === 'error' ? 'error-log-row' : ''
        }
      />
      
      {/* 键盘操作提示 */}
      <div style={{ 
        marginTop: 8, 
        fontSize: '12px', 
        color: '#999',
        textAlign: 'center' 
      }}>
        提示：使用 ← → 键翻页，Home/End 键快速定位，点击行查看详情
      </div>
    </Card>
  );
};

export default LogTable;
