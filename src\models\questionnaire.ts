import {
  createQuestionnaire,
  deleteQuestionnaire,
  getQuestionnaireDetail,
  getQuestionnaireList,
  updateQuestionnaire,
  updateQuestionnaireStatus,
} from '@/services';
import { message } from 'antd';
import { useCallback, useState } from 'react';

/**
 * 问卷管理数据模型
 */
export default function useQuestionnaireModel() {
  const [questionnaireList, setQuestionnaireList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [questionnaireDetail, setQuestionnaireDetail] =
    useState<API.IQuestionnaireDetail | null>(null);
  const [detailLoading, setDetailLoading] = useState(false);

  // 获取问卷列表
  const fetchQuestionnaireList = useCallback(
    async (params?: API.IQuestionnaireQuery) => {
      setLoading(true);
      try {
        const response = await getQuestionnaireList(params);
        if (response.errCode === 0) {
          setQuestionnaireList(response.data?.list || []);
          setTotal(response.data?.total || 0);
          return response.data;
        } else {
          message.error(response.msg || '获取问卷列表失败');
          return null;
        }
      } catch (error) {
        message.error('获取问卷列表失败');
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 创建问卷
  const createQuestionnaireAction = useCallback(
    async (params: API.ICreateQuestionnaireParams) => {
      try {
        const response = await createQuestionnaire(params);
        if (response.errCode === 0) {
          message.success('创建问卷成功');
          return response.data;
        } else {
          message.error(response.msg || '创建问卷失败');
          return null;
        }
      } catch (error) {
        message.error('创建问卷失败');
        return null;
      }
    },
    [],
  );

  // 更新问卷
  const updateQuestionnaireAction = useCallback(
    async (id: number, params: API.ICreateQuestionnaireParams) => {
      try {
        const response = await updateQuestionnaire(id, params);
        if (response.errCode === 0) {
          message.success('更新问卷成功');
          return response.data;
        } else {
          message.error(response.msg || '更新问卷失败');
          return null;
        }
      } catch (error) {
        message.error('更新问卷失败');
        return null;
      }
    },
    [],
  );

  // 更新问卷状态
  const updateQuestionnaireStatusAction = useCallback(
    async (id: number, params: API.IUpdateQuestionnaireStatusParams) => {
      try {
        const response = await updateQuestionnaireStatus(id, params);
        if (response.errCode === 0) {
          message.success('更新问卷状态成功');
          // 刷新列表
          await fetchQuestionnaireList();
          return response.data;
        } else {
          message.error(response.msg || '更新问卷状态失败');
          return null;
        }
      } catch (error) {
        message.error('更新问卷状态失败');
        return null;
      }
    },
    [fetchQuestionnaireList],
  );

  // 删除问卷
  const deleteQuestionnaireAction = useCallback(
    async (id: number) => {
      try {
        const response = await deleteQuestionnaire(id);
        if (response.errCode === 0) {
          message.success('删除问卷成功');
          // 刷新列表
          await fetchQuestionnaireList();
          return true;
        } else {
          message.error(response.msg || '删除问卷失败');
          return false;
        }
      } catch (error) {
        message.error('删除问卷失败');
        return false;
      }
    },
    [fetchQuestionnaireList],
  );

  // 获取问卷详情
  const fetchQuestionnaireDetail = useCallback(async (id: number) => {
    setDetailLoading(true);
    try {
      const response = await getQuestionnaireDetail(id);
      if (response.errCode === 0) {
        setQuestionnaireDetail(response.data || null);
        return response.data;
      } else {
        message.error(response.msg || '获取问卷详情失败');
        return null;
      }
    } catch (error) {
      message.error('获取问卷详情失败');
      return null;
    } finally {
      setDetailLoading(false);
    }
  }, []);

  return {
    // 状态
    questionnaireList,
    loading,
    total,
    questionnaireDetail,
    detailLoading,

    // 方法
    fetchQuestionnaireList,
    fetchQuestionnaireDetail,
    createQuestionnaireAction,
    updateQuestionnaireAction,
    updateQuestionnaireStatusAction,
    deleteQuestionnaireAction,
  };
}
