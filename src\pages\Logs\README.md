# 📋 日志查询管理页面

## 🎯 功能概述

日志查询管理页面提供了全面的系统日志查询、分析和管理功能，帮助管理员监控系统运行状态，快速定位和解决问题。

## ✨ 主要功能

### 1. 日志查询表单区域
- **日期范围选择器**：支持近 7 天、本月等快捷选项
- **日志级别筛选**：info/warn/error 三个级别
- **关键词搜索框**：匹配日志内容进行模糊搜索
- **操作类型筛选**：问卷创建/提交/统计查询等
- **重置/查询按钮组**：查询时向后端传递完整参数

### 2. 日志统计图表
- **统计卡片**：今日日志总数、错误日志占比、各级别日志数量
- **趋势图**：近 7 天日志数量变化折线图
- **分类统计**：按操作类型的日志数量分布饼图
- **实时更新**：统计数据自动刷新

### 3. 日志列表展示区域
- **表格展示**：使用 Ant Design Table 组件
- **列配置**：日志时间（精确到秒）、日志级别（不同颜色标识）、操作类型、日志内容摘要、用户、响应时间
- **分页功能**：默认 10 条/页，支持自定义页面大小
- **排序功能**：按时间降序排列，支持多列排序
- **搜索过滤**：表格内置过滤器
- **悬停提示**：鼠标悬停显示完整日志内容 tooltip
- **错误日志高亮**：错误日志条目特殊样式显示

### 4. 日志详情模态框
- **完整信息展示**：包含请求 IP、用户 ID、请求参数等元数据
- **日志内容复制**：一键复制完整日志数据
- **错误堆栈信息**：错误日志显示堆栈信息折叠面板
- **结构化展示**：请求参数、响应数据、额外数据分类展示

### 5. 功能交互逻辑
- **实时查询**：表单查询条件变更时实时请求后端 API
- **键盘翻页**：支持键盘翻页和快速定位（← → Home End）
- **自动刷新**：长时间未操作自动刷新日志列表（间隔 5 分钟）
- **智能暂停**：页面不可见时暂停自动刷新，节省资源
- **快速定位**：错误日志条目高亮显示，点击后自动展开详情

### 6. 数据导出功能
- **CSV 导出**：支持按查询条件导出日志数据
- **文件命名**：自动生成带日期的文件名
- **异步下载**：不阻塞 UI 操作

## 🛠️ 技术实现

### 组件架构
```
src/pages/Logs/
├── index.tsx                    # 主页面组件
├── index.less                   # 样式文件
├── components/
│   ├── LogQueryForm.tsx         # 日志查询表单
│   ├── LogStatistics.tsx        # 日志统计图表
│   ├── LogTable.tsx             # 日志列表表格
│   └── LogDetailModal.tsx       # 日志详情模态框
└── README.md                    # 说明文档
```

### 数据模型
- **状态管理**：使用 umi dva model 进行状态管理
- **自动刷新**：定时器管理和页面可见性检测
- **错误处理**：完善的错误提示和加载状态
- **类型安全**：完整的 TypeScript 类型定义

### API 接口
- `GET /api/logs` - 获取日志列表
- `GET /api/logs/statistics` - 获取日志统计
- `GET /api/logs/export` - 导出日志数据
- `GET /api/operation-log/:id` - 获取日志详情
- `GET /auth/schools` - 获取学校列表

### 图表库
- **Ant Design Charts**：基于 G2Plot 的 React 图表库
- **折线图**：日志趋势分析
- **饼图**：操作类型分布

## 🔐 权限控制

### 页面级权限
- **认证校验**：仅登录用户可访问
- **角色控制**：管理员角色权限校验

### 数据级权限
- **学校隔离**：用户只能查看自己学校的日志数据
- **敏感信息**：敏感数据脱敏处理

## ⚡ 性能优化

### 前端优化
- **虚拟滚动**：大数据量下优化列表渲染
- **内容摘要**：日志内容超出 20 字显示省略号
- **异步加载**：图表数据异步加载，避免阻塞 UI
- **防抖处理**：搜索输入防抖优化

### 后端优化
- **分页查询**：避免一次性加载大量数据
- **索引优化**：数据库查询索引优化
- **缓存策略**：统计数据缓存机制

## 📱 响应式设计

- **桌面端**：完整功能展示，多列布局
- **平板端**：自适应布局调整
- **移动端**：单列布局，优化触摸操作

## 🎨 UI/UX 特性

- **现代化设计**：遵循 Ant Design 设计规范
- **状态反馈**：丰富的加载状态和操作反馈
- **错误突出**：错误日志特殊样式显示
- **键盘支持**：完整的键盘导航支持

## 🚀 使用说明

### 1. 访问页面
导航到 `/logs` 查看日志管理页面

### 2. 查询日志
- 选择日期范围、日志级别等筛选条件
- 输入关键词进行内容搜索
- 点击"查询"按钮获取结果

### 3. 查看统计
- 查看顶部统计卡片了解日志概况
- 分析趋势图了解日志变化趋势
- 查看分类统计了解操作分布

### 4. 浏览日志
- 在列表中浏览日志条目
- 使用键盘 ← → 键翻页
- 点击行查看详细信息

### 5. 详情查看
- 查看完整的日志元数据
- 复制日志内容用于分析
- 查看错误堆栈信息

### 6. 导出数据
- 点击"导出"按钮下载 CSV 文件
- 文件包含当前查询条件的所有日志

## 🔄 自动刷新机制

- **定时刷新**：每 5 分钟自动刷新一次
- **智能暂停**：页面不可见时自动暂停
- **手动控制**：用户操作时重置定时器

## 🎯 后续优化

- [ ] 添加日志告警功能
- [ ] 支持日志实时推送
- [ ] 增加更多图表类型
- [ ] 添加日志分析报告
- [ ] 支持日志归档管理
