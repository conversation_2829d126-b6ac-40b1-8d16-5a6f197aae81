import React from 'react';
import { Card, Row, Col, Statistic, Skeleton } from 'antd';
import { FileTextOutlined, ExclamationCircleOutlined, WarningOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Line, Pie } from '@ant-design/charts';

interface LogStatisticsProps {
  data: API.ILogStatistics | null;
  loading?: boolean;
}

/**
 * 日志统计组件
 */
const LogStatistics: React.FC<LogStatisticsProps> = ({
  data,
  loading = false,
}) => {
  // 趋势图配置
  const trendConfig = React.useMemo(() => {
    if (!data?.trend_data) return null;

    const chartData: any[] = [];
    data.trend_data.forEach((item) => {
      chartData.push(
        { date: item.date, count: item.total, type: '总数' },
        { date: item.date, count: item.error, type: '错误' },
        { date: item.date, count: item.warn, type: '警告' },
        { date: item.date, count: item.info, type: '信息' }
      );
    });

    return {
      data: chartData,
      xField: 'date',
      yField: 'count',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000,
        },
      },
      color: ['#1890ff', '#f5222d', '#faad14', '#52c41a'],
      point: {
        size: 3,
        shape: 'circle',
      },
      tooltip: {
        formatter: (datum: any) => {
          return {
            name: datum.type,
            value: `${datum.count} 条`,
          };
        },
      },
      legend: {
        position: 'top' as const,
      },
      yAxis: {
        title: {
          text: '日志数量',
        },
        min: 0,
      },
      xAxis: {
        title: {
          text: '日期',
        },
      },
    };
  }, [data?.trend_data]);

  // 饼图配置
  const pieConfig = React.useMemo(() => {
    if (!data?.operation_distribution) return null;

    return {
      data: data.operation_distribution.map((item) => ({
        type: item.type,
        value: item.count,
        percentage: item.percentage,
      })),
      angleField: 'value',
      colorField: 'type',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name} {percentage}',
      },
      tooltip: {
        formatter: (datum: any) => {
          return {
            name: datum.type,
            value: `${datum.value} 条 (${datum.percentage}%)`,
          };
        },
      },
      interactions: [
        {
          type: 'element-active',
        },
      ],
    };
  }, [data?.operation_distribution]);

  if (loading) {
    return (
      <>
        {/* 统计卡片骨架 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          {[1, 2, 3, 4].map((item) => (
            <Col span={6} key={item}>
              <Card>
                <Skeleton active paragraph={{ rows: 2 }} />
              </Card>
            </Col>
          ))}
        </Row>

        {/* 图表骨架 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Card title="近7天日志趋势">
              <Skeleton active paragraph={{ rows: 8 }} />
            </Card>
          </Col>
          <Col span={8}>
            <Card title="操作类型分布">
              <Skeleton active paragraph={{ rows: 8 }} />
            </Card>
          </Col>
        </Row>
      </>
    );
  }

  if (!data) {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card>
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              暂无统计数据
            </div>
          </Card>
        </Col>
      </Row>
    );
  }

  return (
    <>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日日志总数"
              value={data.today_total}
              suffix="条"
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <Statistic
              title="错误日志"
              value={data.error_count}
              suffix="条"
              prefix={<ExclamationCircleOutlined style={{ color: '#f5222d' }} />}
              valueStyle={{ color: '#f5222d' }}
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
              占比: {data.error_rate.toFixed(1)}%
            </div>
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <Statistic
              title="警告日志"
              value={data.warn_count}
              suffix="条"
              prefix={<WarningOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <Statistic
              title="信息日志"
              value={data.info_count}
              suffix="条"
              prefix={<InfoCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={16}>
          <Card title="近7天日志趋势">
            {trendConfig ? (
              <div style={{ height: 300 }}>
                <Line {...trendConfig} />
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                暂无趋势数据
              </div>
            )}
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title="操作类型分布">
            {pieConfig ? (
              <div style={{ height: 300 }}>
                <Pie {...pieConfig} />
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                暂无分布数据
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default LogStatistics;
