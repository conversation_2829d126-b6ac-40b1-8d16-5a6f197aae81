import React from 'react';
import { Card, Row, Col, Statistic, Skeleton } from 'antd';
import { FileTextOutlined, ExclamationCircleOutlined, WarningOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Line, Pie } from '@ant-design/charts';

interface LogStatisticsProps {
  data: API.IOperationLogStatistics | null;
  loading?: boolean;
}

/**
 * 日志统计组件
 */
const LogStatistics: React.FC<LogStatisticsProps> = ({
  data,
  loading = false,
}) => {
  // 趋势图配置
  const trendConfig = React.useMemo(() => {
    if (!data?.operation_trend) return null;

    const chartData: any[] = [];
    // 现有接口返回的是operation_trend，需要适配
    data.operation_trend.forEach((item: any) => {
      chartData.push({
        date: item.date || item.time,
        count: item.count || item.total,
        type: '操作数量',
      });
    });

    return {
      data: chartData,
      xField: 'date',
      yField: 'count',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000,
        },
      },
      color: ['#1890ff'],
      point: {
        size: 3,
        shape: 'circle',
      },
      tooltip: {
        formatter: (datum: any) => {
          return {
            name: datum.type,
            value: `${datum.count} 条`,
          };
        },
      },
      legend: {
        position: 'top' as const,
      },
      yAxis: {
        title: {
          text: '操作数量',
        },
        min: 0,
      },
      xAxis: {
        title: {
          text: '日期',
        },
      },
    };
  }, [data?.operation_trend]);

  // 饼图配置
  const pieConfig = React.useMemo(() => {
    if (!data?.module_distribution) return null;

    return {
      data: data.module_distribution.map((item: any) => ({
        type: item.module || item.type,
        value: item.count,
        percentage: ((item.count / data.total_operations) * 100).toFixed(1),
      })),
      angleField: 'value',
      colorField: 'type',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name} {percentage}%',
      },
      tooltip: {
        formatter: (datum: any) => {
          return {
            name: datum.type,
            value: `${datum.value} 条 (${datum.percentage}%)`,
          };
        },
      },
      interactions: [
        {
          type: 'element-active',
        },
      ],
    };
  }, [data?.module_distribution, data?.total_operations]);

  if (loading) {
    return (
      <>
        {/* 统计卡片骨架 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          {[1, 2, 3, 4].map((item) => (
            <Col span={6} key={item}>
              <Card>
                <Skeleton active paragraph={{ rows: 2 }} />
              </Card>
            </Col>
          ))}
        </Row>

        {/* 图表骨架 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Card title="近7天日志趋势">
              <Skeleton active paragraph={{ rows: 8 }} />
            </Card>
          </Col>
          <Col span={8}>
            <Card title="操作类型分布">
              <Skeleton active paragraph={{ rows: 8 }} />
            </Card>
          </Col>
        </Row>
      </>
    );
  }

  if (!data) {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card>
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              暂无统计数据
            </div>
          </Card>
        </Col>
      </Row>
    );
  }

  return (
    <>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总操作数"
              value={data.total_operations}
              suffix="条"
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '24px' }}
              formatter={(value) => (
                <span style={{ display: 'inline-flex', alignItems: 'baseline', gap: '4px' }}>
                  <span>{value}</span>
                  <span style={{ fontSize: '14px', color: '#666' }}>条</span>
                </span>
              )}
            />
          </Card>
        </Col>

        <Col span={6}>
          <Card>
            <Statistic
              title="成功操作"
              value={data.success_operations || 0}
              prefix={<InfoCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '24px' }}
              formatter={(value) => (
                <span style={{ display: 'inline-flex', alignItems: 'baseline', gap: '4px' }}>
                  <span>{value}</span>
                  <span style={{ fontSize: '14px', color: '#666' }}>条</span>
                </span>
              )}
            />
          </Card>
        </Col>

        <Col span={6}>
          <Card>
            <Statistic
              title="平均响应时间"
              value={data.avg_response_time || 0}
              prefix={<WarningOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14', fontSize: '24px' }}
              formatter={(value) => (
                <span style={{ display: 'inline-flex', alignItems: 'baseline', gap: '4px' }}>
                  <span>{value}</span>
                  <span style={{ fontSize: '14px', color: '#666' }}>ms</span>
                </span>
              )}
            />
          </Card>
        </Col>

        <Col span={6}>
          <Card>
            <Statistic
              title="成功率"
              value={data.success_rate || 0}
              prefix={<ExclamationCircleOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1', fontSize: '24px' }}
              formatter={(value) => (
                <span style={{ display: 'inline-flex', alignItems: 'baseline', gap: '4px' }}>
                  <span>{value}</span>
                  <span style={{ fontSize: '14px', color: '#666' }}>%</span>
                </span>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={16}>
          <Card title="操作趋势">
            {trendConfig ? (
              <div style={{ height: 300 }}>
                <Line {...trendConfig} />
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                暂无趋势数据
              </div>
            )}
          </Card>
        </Col>

        <Col span={8}>
          <Card title="模块分布">
            {pieConfig ? (
              <div style={{ height: 300 }}>
                <Pie {...pieConfig} />
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                暂无分布数据
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default LogStatistics;
