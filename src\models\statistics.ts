import {
  getSchoolList,
  getSchoolResponseTrend,
  getSchoolStatistics,
  getTeacherKeywords,
  getTeacherRanking,
  getTeacherScoreDistribution,
  getTeacherStatistics,
} from '@/services';
import { message } from 'antd';
import { useCallback, useState } from 'react';

/**
 * 统计分析数据模型
 */
export default function useStatisticsModel() {
  // 加载状态
  const [loading, setLoading] = useState(false);
  const [chartLoading, setChartLoading] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);

  // 学校列表
  const [schoolList, setSchoolList] = useState<API.ISSoSchoolInfo[]>([]);

  // 学校统计数据
  const [schoolStatistics, setSchoolStatistics] =
    useState<API.ISchoolStatistics | null>(null);

  // 趋势数据
  const [trendData, setTrendData] = useState<API.ITrendData[]>([]);

  // 教师排名数据
  const [teacherRanking, setTeacherRanking] = useState<API.ITeacherRanking[]>(
    [],
  );
  const [teacherTotal, setTeacherTotal] = useState(0);

  // 教师详情数据
  const [teacherDetail, setTeacherDetail] =
    useState<API.ITeacherStatistics | null>(null);
  const [scoreDistribution, setScoreDistribution] = useState<
    API.IScoreDistribution[]
  >([]);
  const [keywordData, setKeywordData] = useState<API.IKeywordData[]>([]);

  // 筛选条件
  const [filters, setFilters] = useState<API.IStatisticsQuery>({});

  // 获取学校列表
  const fetchSchoolList = useCallback(async () => {
    try {
      const response = await getSchoolList();
      if (response.errCode === 0) {
        setSchoolList(response.data || []);
        return response.data;
      } else {
        message.error(response.msg || '获取学校列表失败');
        return [];
      }
    } catch (error) {
      message.error('获取学校列表失败');
      return [];
    }
  }, []);

  // 获取学校统计数据
  const fetchSchoolStatistics = useCallback(
    async (params?: API.IStatisticsQuery) => {
      setLoading(true);
      try {
        const response = await getSchoolStatistics(params);
        if (response.errCode === 0) {
          setSchoolStatistics(response.data || null);
          return response.data;
        } else {
          message.error(response.msg || '获取学校统计数据失败');
          return null;
        }
      } catch (error) {
        message.error('获取学校统计数据失败');
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取趋势数据
  const fetchTrendData = useCallback(
    async (schoolId: string, params?: API.IStatisticsQuery) => {
      setChartLoading(true);
      try {
        const response = await getSchoolResponseTrend(schoolId, params);
        if (response.errCode === 0) {
          setTrendData(response.data || []);
          return response.data;
        } else {
          message.error(response.msg || '获取趋势数据失败');
          return [];
        }
      } catch (error) {
        message.error('获取趋势数据失败');
        return [];
      } finally {
        setChartLoading(false);
      }
    },
    [],
  );

  // 获取教师排名数据
  const fetchTeacherRanking = useCallback(
    async (params?: API.IStatisticsQuery) => {
      setLoading(true);
      try {
        const response = await getTeacherRanking(params);
        if (response.errCode === 0) {
          setTeacherRanking(response.data?.list || []);
          setTeacherTotal(response.data?.total || 0);
          return response.data;
        } else {
          message.error(response.msg || '获取教师排名失败');
          return null;
        }
      } catch (error) {
        message.error('获取教师排名失败');
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取教师详情数据
  const fetchTeacherDetail = useCallback(
    async (teacherId: string, params?: API.IStatisticsQuery) => {
      setModalLoading(true);
      try {
        // 并行获取教师统计、评分分布、关键词数据
        const [statsResponse, distributionResponse, keywordsResponse] =
          await Promise.all([
            getTeacherStatistics({ ...params, sso_teacher_id: teacherId }),
            getTeacherScoreDistribution(teacherId, params),
            getTeacherKeywords(teacherId, params),
          ]);

        if (statsResponse.errCode === 0) {
          setTeacherDetail(statsResponse.data || null);
        }

        if (distributionResponse.errCode === 0) {
          setScoreDistribution(distributionResponse.data || []);
        }

        if (keywordsResponse.errCode === 0) {
          setKeywordData(keywordsResponse.data || []);
        }

        return {
          detail: statsResponse.data,
          distribution: distributionResponse.data,
          keywords: keywordsResponse.data,
        };
      } catch (error) {
        message.error('获取教师详情失败');
        return null;
      } finally {
        setModalLoading(false);
      }
    },
    [],
  );

  // 更新筛选条件并刷新数据
  const updateFilters = useCallback(
    async (newFilters: API.IStatisticsQuery) => {
      setFilters(newFilters);

      // 刷新所有数据
      await Promise.all([
        fetchSchoolStatistics(newFilters),
        fetchTeacherRanking(newFilters),
        newFilters.sso_school_id &&
          fetchTrendData(newFilters.sso_school_id, newFilters),
      ]);
    },
    [fetchSchoolStatistics, fetchTeacherRanking, fetchTrendData],
  );

  // 清空教师详情数据
  const clearTeacherDetail = useCallback(() => {
    setTeacherDetail(null);
    setScoreDistribution([]);
    setKeywordData([]);
  }, []);

  return {
    // 状态
    loading,
    chartLoading,
    modalLoading,
    schoolList,
    schoolStatistics,
    trendData,
    teacherRanking,
    teacherTotal,
    teacherDetail,
    scoreDistribution,
    keywordData,
    filters,

    // 方法
    fetchSchoolList,
    fetchSchoolStatistics,
    fetchTrendData,
    fetchTeacherRanking,
    fetchTeacherDetail,
    updateFilters,
    clearTeacherDetail,
  };
}
