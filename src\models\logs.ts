import { useCallback, useState, useRef } from 'react';
import { message } from 'antd';
import {
  getLogList,
  getLogStatistics,
  getOperationLogDetail,
  exportLogs,
  getSchoolList,
} from '@/services';

/**
 * 日志管理数据模型
 */
export default function useLogsModel() {
  // 加载状态
  const [loading, setLoading] = useState(false);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);

  // 学校列表
  const [schoolList, setSchoolList] = useState<API.ISSoSchoolInfo[]>([]);

  // 日志列表数据
  const [logList, setLogList] = useState<API.IOperationLogDetail[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 日志统计数据
  const [statistics, setStatistics] = useState<API.ILogStatistics | null>(null);

  // 日志详情数据
  const [logDetail, setLogDetail] = useState<API.IOperationLogDetail | null>(null);

  // 查询条件
  const [queryParams, setQueryParams] = useState<API.ILogQuery>({});

  // 自动刷新定时器
  const autoRefreshTimer = useRef<NodeJS.Timeout | null>(null);

  // 获取学校列表
  const fetchSchoolList = useCallback(async () => {
    try {
      const response = await getSchoolList();
      if (response.errCode === 0) {
        setSchoolList(response.data || []);
        return response.data;
      } else {
        message.error(response.msg || '获取学校列表失败');
        return [];
      }
    } catch (error) {
      message.error('获取学校列表失败');
      return [];
    }
  }, []);

  // 获取日志列表
  const fetchLogList = useCallback(async (params?: API.ILogQuery) => {
    setLoading(true);
    try {
      const queryData = {
        ...queryParams,
        ...params,
        page: params?.page || currentPage,
        limit: params?.limit || pageSize,
      };

      const response = await getLogList(queryData);
      if (response.errCode === 0) {
        setLogList(response.data?.list || []);
        setTotal(response.data?.total || 0);
        if (params?.page) setCurrentPage(params.page);
        if (params?.limit) setPageSize(params.limit);
        return response.data;
      } else {
        message.error(response.msg || '获取日志列表失败');
        return null;
      }
    } catch (error) {
      message.error('获取日志列表失败');
      return null;
    } finally {
      setLoading(false);
    }
  }, [queryParams, currentPage, pageSize]);

  // 获取日志统计
  const fetchStatistics = useCallback(async (params?: API.ILogQuery) => {
    setStatisticsLoading(true);
    try {
      const response = await getLogStatistics(params);
      if (response.errCode === 0) {
        setStatistics(response.data || null);
        return response.data;
      } else {
        message.error(response.msg || '获取日志统计失败');
        return null;
      }
    } catch (error) {
      message.error('获取日志统计失败');
      return null;
    } finally {
      setStatisticsLoading(false);
    }
  }, []);

  // 获取日志详情
  const fetchLogDetail = useCallback(async (id: number) => {
    setDetailLoading(true);
    try {
      const response = await getOperationLogDetail(id);
      if (response.errCode === 0) {
        setLogDetail(response.data || null);
        return response.data;
      } else {
        message.error(response.msg || '获取日志详情失败');
        return null;
      }
    } catch (error) {
      message.error('获取日志详情失败');
      return null;
    } finally {
      setDetailLoading(false);
    }
  }, []);

  // 导出日志
  const handleExportLogs = useCallback(async (params?: API.ILogQuery) => {
    setExportLoading(true);
    try {
      const response = await exportLogs(params);
      
      // 创建下载链接
      const blob = new Blob([response], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `logs_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      message.success('日志导出成功');
    } catch (error) {
      message.error('日志导出失败');
    } finally {
      setExportLoading(false);
    }
  }, []);

  // 更新查询条件并刷新数据
  const updateQuery = useCallback(async (newParams: API.ILogQuery) => {
    setQueryParams(newParams);
    setCurrentPage(1); // 重置到第一页
    
    // 并行获取列表和统计数据
    await Promise.all([
      fetchLogList({ ...newParams, page: 1 }),
      fetchStatistics(newParams),
    ]);
  }, [fetchLogList, fetchStatistics]);

  // 分页变更
  const handlePageChange = useCallback(async (page: number, size?: number) => {
    await fetchLogList({ page, limit: size });
  }, [fetchLogList]);

  // 启动自动刷新
  const startAutoRefresh = useCallback(() => {
    if (autoRefreshTimer.current) {
      clearInterval(autoRefreshTimer.current);
    }
    
    autoRefreshTimer.current = setInterval(() => {
      fetchLogList();
      fetchStatistics(queryParams);
    }, 5 * 60 * 1000); // 5分钟刷新一次
  }, [fetchLogList, fetchStatistics, queryParams]);

  // 停止自动刷新
  const stopAutoRefresh = useCallback(() => {
    if (autoRefreshTimer.current) {
      clearInterval(autoRefreshTimer.current);
      autoRefreshTimer.current = null;
    }
  }, []);

  // 清空日志详情
  const clearLogDetail = useCallback(() => {
    setLogDetail(null);
  }, []);

  // 重置查询条件
  const resetQuery = useCallback(async () => {
    setQueryParams({});
    setCurrentPage(1);
    await Promise.all([
      fetchLogList({ page: 1 }),
      fetchStatistics({}),
    ]);
  }, [fetchLogList, fetchStatistics]);

  return {
    // 状态
    loading,
    statisticsLoading,
    exportLoading,
    detailLoading,
    schoolList,
    logList,
    total,
    currentPage,
    pageSize,
    statistics,
    logDetail,
    queryParams,

    // 方法
    fetchSchoolList,
    fetchLogList,
    fetchStatistics,
    fetchLogDetail,
    handleExportLogs,
    updateQuery,
    handlePageChange,
    startAutoRefresh,
    stopAutoRefresh,
    clearLogDetail,
    resetQuery,
  };
}
