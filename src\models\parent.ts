import {
  getQuestionnaireForParent,
  getStudentTeachers,
  submitParentEvaluation,
  verifyParentPhone,
} from '@/services';
import { message } from 'antd';
import { useCallback, useState } from 'react';

/**
 * 家长端数据模型
 * @description 管理家长端问卷填写流程的状态和数据
 */
export default function useParentModel() {
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<
    'phone' | 'student' | 'evaluation'
  >('phone');

  // 家长信息
  const [parentPhone, setParentPhone] = useState('');
  const [parentInfo, setParentInfo] = useState<any>(null);

  // 学生信息
  const [studentList, setStudentList] = useState<API.ISSoStudentInfo[]>([]);
  const [selectedStudent, setSelectedStudent] =
    useState<API.ISSoStudentInfo | null>(null);

  // 问卷信息
  const [questionnaireInfo, setQuestionnaireInfo] =
    useState<API.IParentQuestionnaireInfo | null>(null);
  const [teacherList, setTeacherList] = useState<API.IStudentTeacherInfo[]>([]);

  // 评价数据
  const [evaluationData, setEvaluationData] = useState<API.IEvaluationData>({
    school_rating: 0,
    school_comment: '',
    teacher_evaluations: [],
  });

  // 本地缓存键名
  const getCacheKey = (suffix: string) => {
    return `parent_evaluation_${parentPhone}_${selectedStudent?.id}_${suffix}`;
  };

  // 验证家长手机号
  const verifyPhone = useCallback(async (phone: string) => {
    setLoading(true);
    try {
      const response = await verifyParentPhone({ phone });
      if (response.errCode === 0 && response.data?.success) {
        setParentPhone(phone);
        setParentInfo(response.data.parent_info);
        setStudentList(response.data.students);
        setCurrentStep('student');
        message.success('手机号验证成功');
        return true;
      } else {
        message.error(response.msg || '手机号验证失败，请检查是否已关联学生');
        return false;
      }
    } catch (error) {
      message.error('验证失败，请稍后重试');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // 选择学生
  const selectStudent = useCallback(
    async (student: API.ISSoStudentInfo) => {
      setLoading(true);
      try {
        setSelectedStudent(student);

        // 获取问卷信息
        const questionnaireResponse = await getQuestionnaireForParent({
          student_id: student.id,
        });

        if (questionnaireResponse.errCode === 0 && questionnaireResponse.data) {
          setQuestionnaireInfo(questionnaireResponse.data);

          // 检查是否已提交
          if (questionnaireResponse.data.is_submitted) {
            message.warning('该学生的问卷已提交，无法重复填写');
            return false;
          }

          // 获取教师列表
          const teachersResponse = await getStudentTeachers({
            student_id: student.id,
            questionnaire_id: questionnaireResponse.data.questionnaire_id,
          });

          if (teachersResponse.errCode === 0) {
            setTeacherList(teachersResponse.data || []);

            // 尝试从本地缓存恢复数据
            const cachedData = localStorage.getItem(getCacheKey('evaluation'));
            if (cachedData) {
              try {
                const parsed = JSON.parse(cachedData);
                setEvaluationData(parsed);
                message.info('已恢复之前填写的内容');
              } catch (e) {
                console.warn('缓存数据解析失败:', e);
              }
            }

            setCurrentStep('evaluation');
            return true;
          } else {
            message.error(teachersResponse.msg || '获取教师列表失败');
            return false;
          }
        } else {
          message.error(questionnaireResponse.msg || '获取问卷信息失败');
          return false;
        }
      } catch (error) {
        message.error('操作失败，请稍后重试');
        return false;
      } finally {
        setLoading(false);
      }
    },
    [parentPhone],
  );

  // 更新评价数据
  const updateEvaluationData = useCallback(
    (data: Partial<API.IEvaluationData>) => {
      const newData = { ...evaluationData, ...data };
      setEvaluationData(newData);

      // 保存到本地缓存
      if (selectedStudent) {
        localStorage.setItem(
          getCacheKey('evaluation'),
          JSON.stringify(newData),
        );
      }
    },
    [evaluationData, selectedStudent, parentPhone],
  );

  // 更新学校评分
  const updateSchoolRating = useCallback(
    (rating: number, comment?: string) => {
      updateEvaluationData({
        school_rating: rating,
        school_comment: comment || evaluationData.school_comment,
      });
    },
    [updateEvaluationData, evaluationData.school_comment],
  );

  // 更新教师评分
  const updateTeacherRating = useCallback(
    (teacherId: string, rating: number, comment?: string) => {
      const existingIndex = evaluationData.teacher_evaluations.findIndex(
        (evaluation) => evaluation.teacher_id === teacherId,
      );

      const newEvaluation = {
        teacher_id: teacherId,
        rating,
        comment: comment || '',
      };
      let newTeacherEvaluations;

      if (existingIndex >= 0) {
        newTeacherEvaluations = [...evaluationData.teacher_evaluations];
        newTeacherEvaluations[existingIndex] = newEvaluation;
      } else {
        newTeacherEvaluations = [
          ...evaluationData.teacher_evaluations,
          newEvaluation,
        ];
      }

      updateEvaluationData({
        teacher_evaluations: newTeacherEvaluations,
      });
    },
    [evaluationData.teacher_evaluations, updateEvaluationData],
  );

  // 检查是否所有教师都已评分
  const isAllTeachersRated = useCallback(() => {
    const unratedTeachers = teacherList.filter(
      (teacher) =>
        !teacher.is_evaluated &&
        !evaluationData.teacher_evaluations.some(
          (evaluation) => evaluation.teacher_id === teacher.teacher_id,
        ),
    );
    return unratedTeachers.length === 0 && evaluationData.school_rating > 0;
  }, [teacherList, evaluationData]);

  // 重置状态
  const resetState = useCallback(() => {
    setCurrentStep('phone');
    setParentPhone('');
    setParentInfo(null);
    setStudentList([]);
    setSelectedStudent(null);
    setQuestionnaireInfo(null);
    setTeacherList([]);
    setEvaluationData({
      school_rating: 0,
      school_comment: '',
      teacher_evaluations: [],
    });
  }, []);

  // 提交评价
  const submitEvaluation = useCallback(async () => {
    if (!selectedStudent || !questionnaireInfo || !isAllTeachersRated()) {
      message.warning('请完成所有评分后再提交');
      return false;
    }

    setLoading(true);
    try {
      const response = await submitParentEvaluation({
        questionnaire_id: questionnaireInfo.questionnaire_id,
        student_id: selectedStudent.id,
        parent_phone: parentPhone,
        evaluation_data: evaluationData,
      });

      if (response.errCode === 0) {
        message.success('提交成功！感谢您的评价');

        // 清除本地缓存
        localStorage.removeItem(getCacheKey('evaluation'));

        // 重置状态
        resetState();
        return true;
      } else {
        message.error(response.msg || '提交失败，请稍后重试');
        return false;
      }
    } catch (error) {
      message.error('提交失败，请稍后重试');
      return false;
    } finally {
      setLoading(false);
    }
  }, [
    selectedStudent,
    questionnaireInfo,
    parentPhone,
    evaluationData,
    isAllTeachersRated,
  ]);

  // 返回上一步
  const goBack = useCallback(() => {
    if (currentStep === 'evaluation') {
      setCurrentStep('student');
      setSelectedStudent(null);
      setQuestionnaireInfo(null);
      setTeacherList([]);
    } else if (currentStep === 'student') {
      setCurrentStep('phone');
      setStudentList([]);
      setParentInfo(null);
    }
  }, [currentStep]);

  return {
    // 状态
    loading,
    currentStep,
    parentPhone,
    parentInfo,
    studentList,
    selectedStudent,
    questionnaireInfo,
    teacherList,
    evaluationData,

    // 计算属性
    isAllTeachersRated: isAllTeachersRated(),

    // 方法
    verifyPhone,
    selectStudent,
    updateSchoolRating,
    updateTeacherRating,
    submitEvaluation,
    resetState,
    goBack,
  };
}
