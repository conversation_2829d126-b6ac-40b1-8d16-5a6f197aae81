import {
  DeleteOutlined,
  EditOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Form,
  Modal,
  Row,
  Select,
  Space,
  Table,
  Tag,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import './index.less';

const { Option } = Select;
const { confirm } = Modal;

/**
 * 问卷列表页面
 */
const QuestionnaireList: React.FC = () => {
  const {
    questionnaireList,
    loading,
    total,
    fetchQuestionnaireList,
    updateQuestionnaireStatusAction,
    deleteQuestionnaireAction,
  } = useModel('questionnaire');

  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  // 搜索处理
  const handleSearch = async (values?: any) => {
    const params: API.IQuestionnaireQuery = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...values,
    };

    await fetchQuestionnaireList(params);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setPagination({ current: 1, pageSize: 10 });
    handleSearch();
  };

  // 分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
    const formValues = form.getFieldsValue();
    handleSearch({ ...formValues, page, limit: pageSize });
  };

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusMap = {
      draft: { color: 'default', text: '草稿' },
      published: { color: 'success', text: '发布' },
      closed: { color: 'error', text: '关闭' },
    };
    const config = statusMap[status as keyof typeof statusMap] || {
      color: 'default',
      text: status,
    };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 状态变更
  const handleStatusChange = (id: number, status: string) => {
    const statusText = status === 'published' ? '发布' : '关闭';
    confirm({
      title: `确认${statusText}问卷？`,
      content: `确定要${statusText}这个问卷吗？`,
      onOk: () => updateQuestionnaireStatusAction(id, { status }),
    });
  };

  // 删除问卷
  const handleDelete = (id: number) => {
    confirm({
      title: '确认删除？',
      content: '删除后无法恢复，确定要删除这个问卷吗？',
      okType: 'danger',
      onOk: () => deleteQuestionnaireAction(id),
    });
  };

  // 操作按钮
  const renderActions = (record: any) => {
    return (
      <Space>
        <Button
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => history.push(`/questionnaire/edit/${record.id}`)}
        >
          编辑
        </Button>

        {record.status === 'draft' && (
          <Button
            type="link"
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() => handleStatusChange(record.id, 'published')}
          >
            发布
          </Button>
        )}

        {record.status === 'published' && (
          <Button
            type="link"
            size="small"
            icon={<StopOutlined />}
            onClick={() => handleStatusChange(record.id, 'closed')}
          >
            关闭
          </Button>
        )}

        <Button
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDelete(record.id)}
        >
          删除
        </Button>
      </Space>
    );
  };

  // 表格列定义
  const columns: ColumnsType<any> = [
    {
      title: '问卷标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
      width: 120,
      render: (month: string) => dayjs(month).format('YYYY-MM'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: renderStatusTag,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
      sorter: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => renderActions(record),
    },
  ];

  // 初始化数据
  useEffect(() => {
    handleSearch();
  }, []);

  return (
    <div className="questionnaire-list">
      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form form={form} layout="inline" onFinish={handleSearch}>
          <Row gutter={16} style={{ width: '100%' }}>
            <Col span={8}>
              <Form.Item name="month" label="月份">
                <DatePicker.MonthPicker
                  placeholder="请选择月份"
                  format="YYYY-MM"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="status" label="状态">
                <Select placeholder="请选择状态" allowClear>
                  <Option value="draft">草稿</Option>
                  <Option value="published">发布</Option>
                  <Option value="closed">关闭</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    搜索
                  </Button>
                  <Button onClick={handleReset}>重置</Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作栏 */}
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => history.push('/questionnaire/create')}
          >
            创建问卷
          </Button>
        </div>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={questionnaireList}
          loading={loading}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
        />
      </Card>
    </div>
  );
};

export default QuestionnaireList;
