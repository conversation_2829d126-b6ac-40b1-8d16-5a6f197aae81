import React, { useState } from 'react';
import {
  Modal,
  Descriptions,
  Tag,
  Button,
  Space,
  Collapse,
  Typography,
  message,
  Spin
} from 'antd';
import {
  CopyOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

interface LogDetailModalProps {
  visible: boolean;
  loading?: boolean;
  data: API.IOperationLogDetail | null;
  onClose: () => void;
}

/**
 * 日志详情模态框组件
 */
const LogDetailModal: React.FC<LogDetailModalProps> = ({
  visible,
  loading = false,
  data,
  onClose,
}) => {
  const [copyLoading, setCopyLoading] = useState(false);

  // 获取日志级别标签
  const getLevelTag = (level?: string) => {
    switch (level) {
      case 'error':
        return (
          <Tag color="error" icon={<ExclamationCircleOutlined />}>
            错误
          </Tag>
        );
      case 'warn':
        return (
          <Tag color="warning" icon={<WarningOutlined />}>
            警告
          </Tag>
        );
      case 'info':
      default:
        return (
          <Tag color="success" icon={<InfoCircleOutlined />}>
            信息
          </Tag>
        );
    }
  };

  // 获取操作类型显示文本
  const getOperationTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      questionnaire_create: '问卷创建',
      questionnaire_update: '问卷编辑',
      questionnaire_delete: '问卷删除',
      response_submit: '评价提交',
      statistics_query: '统计查询',
      user_login: '用户登录',
      user_logout: '用户登出',
    };
    return typeMap[type] || type;
  };

  // 复制日志内容
  const handleCopy = async () => {
    if (!data) return;

    setCopyLoading(true);
    try {
      const logContent = JSON.stringify(data, null, 2);
      await navigator.clipboard.writeText(logContent);
      message.success('日志内容已复制到剪贴板');
    } catch (error) {
      message.error('复制失败，请手动选择复制');
    } finally {
      setCopyLoading(false);
    }
  };

  // 格式化JSON数据
  const formatJsonData = (data: any) => {
    if (!data) return '-';
    if (typeof data === 'string') return data;
    return JSON.stringify(data, null, 2);
  };

  return (
    <Modal
      title={
        <Space>
          {data && getLevelTag(data.level)}
          日志详情
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="copy" icon={<CopyOutlined />} loading={copyLoading} onClick={handleCopy}>
          复制内容
        </Button>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
    >
      <Spin spinning={loading}>
        {data ? (
          <div>
            {/* 基本信息 */}
            <Descriptions title="基本信息" bordered column={2} size="small">
              <Descriptions.Item label="日志ID">{data.id}</Descriptions.Item>
              <Descriptions.Item label="日志时间">
                {dayjs(data.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="日志级别">
                {getLevelTag(data.level)}
              </Descriptions.Item>
              <Descriptions.Item label="操作类型">
                <Tag color="blue">{getOperationTypeText(data.operation_type)}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="用户名称">
                {data.user_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="响应时间">
                {data.response_time ? `${data.response_time}ms` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="操作结果">
                <Tag color={data.result === 'success' ? 'success' : 'error'}>
                  {data.result || '-'}
                </Tag>
              </Descriptions.Item>
            </Descriptions>

            {/* 请求信息 */}
            <Descriptions title="请求信息" bordered column={1} size="small" style={{ marginTop: 16 }}>
              <Descriptions.Item label="请求IP">
                {data.ip_address || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="用户代理">
                <Text ellipsis={{ tooltip: data.user_agent }}>
                  {data.user_agent || '-'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="日志描述">
                <Paragraph copyable>{data.description}</Paragraph>
              </Descriptions.Item>
            </Descriptions>

            {/* 详细数据 */}
            <div style={{ marginTop: 16 }}>
              <Collapse>
                {data.request_params && (
                  <Panel header="请求参数" key="request">
                    <pre style={{
                      background: '#f5f5f5',
                      padding: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      maxHeight: '200px',
                      overflow: 'auto'
                    }}>
                      {formatJsonData(data.request_params)}
                    </pre>
                  </Panel>
                )}

                {data.response_data && (
                  <Panel header="响应数据" key="response">
                    <pre style={{
                      background: '#f5f5f5',
                      padding: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      maxHeight: '200px',
                      overflow: 'auto'
                    }}>
                      {formatJsonData(data.response_data)}
                    </pre>
                  </Panel>
                )}

                {data.error_stack && (
                  <Panel header="错误堆栈" key="error">
                    <pre style={{
                      background: '#fff2f0',
                      padding: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      maxHeight: '300px',
                      overflow: 'auto',
                      color: '#a8071a'
                    }}>
                      {data.error_stack}
                    </pre>
                  </Panel>
                )}

                {data.extra_data && (
                  <Panel header="额外数据" key="extra">
                    <pre style={{
                      background: '#f5f5f5',
                      padding: '12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      maxHeight: '200px',
                      overflow: 'auto'
                    }}>
                      {formatJsonData(data.extra_data)}
                    </pre>
                  </Panel>
                )}
              </Collapse>
            </div>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            暂无日志详情数据
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default LogDetailModal;
