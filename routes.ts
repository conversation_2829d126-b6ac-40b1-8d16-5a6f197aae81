const routes: IBestAFSRoute[] = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    name: '首页',
    path: '/home',
    wrappers: ['@/wrappers/auth'],
    component: './Home',
  },
  {
    name: '问卷管理',
    path: '/questionnaire',
    wrappers: ['@/wrappers/auth'],
    routes: [
      {
        path: '',
        redirect: 'list',
      },
      {
        name: '问卷列表',
        path: 'list',
        component: './Questionnaire/List',
      },
      {
        name: '创建问卷',
        path: 'create',
        component: './Questionnaire/Create',
        hideInMenu: true,
      },
      {
        name: '编辑问卷',
        path: 'edit/:id',
        component: './Questionnaire/Create',
        hideInMenu: true,
      },
    ],
  },
  {
    name: '统计分析',
    path: '/statistics',
    wrappers: ['@/wrappers/auth'],
    component: './Statistics',
  },
  {
    name: '家长端',
    path: '/parent',
    component: './Parent',
    hideInMenu: true,
  },
  {
    name: '星级评分演示',
    path: '/star-rating-demo',
    component: './StarRatingDemo',
    hideInMenu: true,
  },
  {
    name: '权限演示',
    path: '/access',
    wrappers: ['@/wrappers/auth'],
    component: './Access',
  },
];

export default routes;
