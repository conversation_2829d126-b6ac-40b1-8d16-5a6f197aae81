import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import { useModel } from '@umijs/max';
import LogQueryForm from './components/LogQueryForm';
import LogStatistics from './components/LogStatistics';
import LogTable from './components/LogTable';
import LogDetailModal from './components/LogDetailModal';
import './index.less';

/**
 * 日志管理页面
 */
const Logs: React.FC = () => {
  const {
    // 状态
    loading,
    statisticsLoading,
    detailLoading,
    logList,
    total,
    currentPage,
    pageSize,
    statistics,
    logDetail,

    // 方法
    fetchLogList,
    fetchStatistics,
    fetchLogDetail,
    updateQuery,
    handlePageChange,
    startAutoRefresh,
    stopAutoRefresh,
    clearLogDetail,
    resetQuery,
  } = useModel('logs');

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      try {
        // 获取初始数据（近7天）
        const defaultQuery = {
          start_time: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
          end_time: new Date().toISOString(),
        };

        await Promise.all([
          fetchLogList(defaultQuery),
          fetchStatistics(defaultQuery),
        ]);

        // 启动自动刷新
        startAutoRefresh();
      } catch (error) {
        message.error('初始化数据失败');
      }
    };

    initData();

    // 组件卸载时停止自动刷新
    return () => {
      stopAutoRefresh();
    };
  }, [fetchLogList, fetchStatistics, startAutoRefresh, stopAutoRefresh]);

  // 处理查询
  const handleQuery = async (queryParams: API.IOperationLogQuery) => {
    await updateQuery(queryParams);
  };

  // 处理重置
  const handleReset = async () => {
    await resetQuery();
  };



  // 处理行点击
  const handleRowClick = async (record: API.IOperationLogDetail) => {
    setModalVisible(true);
    await fetchLogDetail(record.id);
  };

  // 关闭模态框
  const handleModalClose = () => {
    setModalVisible(false);
    clearLogDetail();
  };

  // 页面可见性变化处理（用户切换标签页时暂停/恢复自动刷新）
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopAutoRefresh();
      } else {
        startAutoRefresh();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [startAutoRefresh, stopAutoRefresh]);

  return (
    <div className="logs-page">
      {/* 查询表单 */}
      <LogQueryForm
        loading={loading}
        onQuery={handleQuery}
        onReset={handleReset}
      />

      {/* 统计图表 */}
      <LogStatistics
        data={statistics}
        loading={statisticsLoading}
      />

      {/* 日志列表 */}
      <LogTable
        data={logList}
        total={total}
        currentPage={currentPage}
        pageSize={pageSize}
        loading={loading}
        onPageChange={handlePageChange}
        onRowClick={handleRowClick}
      />

      {/* 日志详情模态框 */}
      <LogDetailModal
        visible={modalVisible}
        loading={detailLoading}
        data={logDetail}
        onClose={handleModalClose}
      />
    </div>
  );
};

export default Logs;
