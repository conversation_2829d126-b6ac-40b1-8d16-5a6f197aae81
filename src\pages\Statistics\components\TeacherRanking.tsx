import { TrophyOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Card, Input, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useState } from 'react';

interface TeacherRankingProps {
  data: API.ITeacherRanking[];
  total: number;
  loading?: boolean;
  onTeacherClick: (teacherId: string) => void;
}

/**
 * 教师评分排行榜组件
 */
const TeacherRanking: React.FC<TeacherRankingProps> = ({
  data,
  loading = false,
  onTeacherClick,
}) => {
  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState(data);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    if (!value) {
      setFilteredData(data);
    } else {
      const filtered = data.filter(
        (teacher) =>
          teacher.sso_teacher_name?.includes(value) ||
          teacher.sso_teacher_subject?.includes(value) ||
          teacher.sso_teacher_department?.includes(value),
      );
      setFilteredData(filtered);
    }
  };

  // 重置搜索
  const handleReset = () => {
    setSearchText('');
    setFilteredData(data);
  };

  // 更新数据时重置筛选
  React.useEffect(() => {
    setFilteredData(data);
    setSearchText('');
  }, [data]);

  // 获取排名图标
  const getRankIcon = (rank: number) => {
    if (rank === 1) return <TrophyOutlined style={{ color: '#faad14' }} />;
    if (rank === 2) return <TrophyOutlined style={{ color: '#d9d9d9' }} />;
    if (rank === 3) return <TrophyOutlined style={{ color: '#cd7f32' }} />;
    return <span style={{ color: '#999' }}>{rank}</span>;
  };

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 90) return '#52c41a';
    if (score >= 80) return '#faad14';
    if (score >= 70) return '#fa8c16';
    return '#f5222d';
  };

  // 表格列定义
  const columns: ColumnsType<API.ITeacherRanking> = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 80,
      align: 'center',
      render: (rank: number) => getRankIcon(rank),
    },
    {
      title: '教师信息',
      key: 'teacher',
      render: (_, record) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 500 }}>
              {record.sso_teacher_name || '未知教师'}
            </div>
            <div style={{ fontSize: '12px', color: '#999' }}>
              {record.sso_teacher_department || '未知部门'}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '学科',
      dataIndex: 'sso_teacher_subject',
      key: 'subject',
      width: 120,
      render: (subject: string) => (
        <Tag color="blue">{subject || '未知学科'}</Tag>
      ),
    },
    {
      title: '平均分',
      dataIndex: 'average_score',
      key: 'average_score',
      width: 100,
      align: 'center',
      sorter: (a, b) => (a.average_score || 0) - (b.average_score || 0),
      render: (score: number) => (
        <span style={{ color: getScoreColor(score), fontWeight: 500 }}>
          {score?.toFixed(1) || 0}
        </span>
      ),
    },
    {
      title: '评价人数',
      dataIndex: 'evaluation_count',
      key: 'evaluation_count',
      width: 100,
      align: 'center',
      sorter: (a, b) => (a.evaluation_count || 0) - (b.evaluation_count || 0),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          onClick={() => onTeacherClick(record.sso_teacher_id)}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <Card
      title="教师评分排行榜"
      extra={
        <Space>
          <Input.Search
            placeholder="搜索教师姓名、学科、部门"
            value={searchText}
            onChange={(e) => handleSearch(e.target.value)}
            onSearch={handleSearch}
            style={{ width: 250 }}
            allowClear
          />
          <Button onClick={handleReset}>重置</Button>
        </Space>
      }
    >
      <Table
        columns={columns}
        dataSource={filteredData}
        loading={loading}
        rowKey="sso_teacher_id"
        pagination={{
          total: filteredData.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }}
        scroll={{ x: 800 }}
        onRow={(record) => ({
          style: { cursor: 'pointer' },
          onClick: () => onTeacherClick(record.sso_teacher_id),
        })}
      />
    </Card>
  );
};

export default TeacherRanking;
