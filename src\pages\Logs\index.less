.logs-page {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;

  // 卡片样式优化
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      
      .ant-card-head-title {
        font-weight: 600;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 20px;
    }
  }

  // 统计卡片样式
  .ant-statistic {
    .ant-statistic-title {
      font-size: 14px;
      color: #8c8c8c;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      font-size: 24px;
      font-weight: 600;
      
      .ant-statistic-content-value {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  // 表格样式优化
  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
    }

    .ant-table-tbody > tr {
      transition: all 0.2s ease;

      &:hover {
        background: #f5f5f5;
      }

      > td {
        border-bottom: 1px solid #f0f0f0;
      }

      // 错误日志行样式
      &.error-log-row {
        background: #fff2f0;
        border-left: 3px solid #f5222d;

        &:hover {
          background: #ffebe8;
        }
      }
    }

    // 表格大小调整
    &.ant-table-small {
      .ant-table-tbody > tr > td {
        padding: 8px;
      }
    }
  }

  // 表单样式
  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-form-item-label {
      font-weight: 500;
    }
  }

  // 标签样式
  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
    display: inline-flex;
    align-items: center;
    gap: 4px;

    // 日志级别标签特殊样式
    &.ant-tag-success {
      background: #f6ffed;
      border-color: #b7eb8f;
      color: #389e0d;
    }

    &.ant-tag-warning {
      background: #fffbe6;
      border-color: #ffe58f;
      color: #d48806;
    }

    &.ant-tag-error {
      background: #fff2f0;
      border-color: #ffccc7;
      color: #cf1322;
    }
  }

  // 按钮样式
  .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.ant-btn-primary {
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

      &:hover {
        box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
        transform: translateY(-1px);
      }
    }
  }

  // 模态框样式
  .ant-modal {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;

      .ant-modal-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    .ant-modal-body {
      padding: 24px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 12px 24px;
    }
  }

  // 描述列表样式
  .ant-descriptions {
    .ant-descriptions-title {
      font-weight: 600;
      color: #262626;
      margin-bottom: 16px;
    }

    &.ant-descriptions-bordered {
      .ant-descriptions-item-label {
        background: #fafafa;
        font-weight: 500;
      }
    }
  }

  // 折叠面板样式
  .ant-collapse {
    .ant-collapse-header {
      font-weight: 500;
      color: #262626;
    }

    .ant-collapse-content-box {
      padding: 16px;
    }
  }

  // 代码块样式
  pre {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  // 空状态样式
  .ant-empty {
    .ant-empty-description {
      color: #8c8c8c;
    }
  }

  // 加载状态样式
  .ant-spin-container {
    min-height: 200px;
  }

  // 日期选择器样式
  .ant-picker {
    border-radius: 6px;
  }

  // 选择器样式
  .ant-select {
    .ant-select-selector {
      border-radius: 6px;
    }
  }

  // 输入框样式
  .ant-input {
    border-radius: 6px;
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .ant-col {
      margin-bottom: 16px;
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .ant-card {
      margin-bottom: 16px;
    }

    .ant-form {
      .ant-row {
        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .ant-table {
      .ant-table-content {
        overflow-x: auto;
      }
    }

    .ant-modal {
      margin: 0;
      max-width: 100vw;
      
      .ant-modal-content {
        border-radius: 0;
      }
    }
  }

  // 打印样式
  @media print {
    .ant-btn,
    .ant-pagination,
    .ant-modal-mask,
    .ant-modal-wrap {
      display: none !important;
    }

    .ant-table {
      .ant-table-tbody > tr {
        break-inside: avoid;
      }
    }
  }
}
