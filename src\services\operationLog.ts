// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 操作日志服务
 * @description 操作日志的创建、查询、统计、清理等操作
 */

/** 创建操作日志 POST /api/operation-log */
export async function createOperationLog(
  params: API.ICreateOperationLogParams,
) {
  return request<API.ResType<API.IOperationLogDetail>>('/api/operation-log', {
    method: 'POST',
    data: params,
  });
}

/** 获取操作日志列表 GET /api/operation-log */
export async function getOperationLogList(params?: API.IOperationLogQuery) {
  return request<API.ResType<API.IOperationLogListResponse>>(
    '/api/operation-log',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取操作日志详情 GET /api/operation-log/:id */
export async function getOperationLogDetail(id: number) {
  return request<API.ResType<API.IOperationLogDetail>>(
    `/api/operation-log/${id}`,
    {
      method: 'GET',
    },
  );
}

/** 获取操作日志统计 GET /api/operation-log/statistics */
export async function getOperationLogStatistics(
  params?: API.IOperationLogQuery,
) {
  return request<API.ResType<API.IOperationLogStatistics>>(
    '/api/operation-log/statistics',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取用户操作历史 GET /api/operation-log/user/:userId/history */
export async function getUserOperationHistory(
  userId: string,
  params?: API.IOperationLogQuery,
) {
  return request<API.ResType<API.IOperationLogListResponse>>(
    `/api/operation-log/user/${userId}/history`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取学校操作日志 GET /api/operation-log/school/:schoolId */
export async function getSchoolOperationLog(
  schoolId: string,
  params?: API.IOperationLogQuery,
) {
  return request<API.ResType<API.IOperationLogListResponse>>(
    `/api/operation-log/school/${schoolId}`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取操作日志趋势 GET /api/operation-log/trend */
export async function getOperationLogTrend(params?: API.IOperationLogQuery) {
  return request<API.ResType<any[]>>('/api/operation-log/trend', {
    method: 'GET',
    params,
  });
}

/** 清理过期日志 POST /api/operation-log/cleanup */
export async function cleanupExpiredLogs(params?: { days?: number }) {
  return request<API.ResType<{ deleted_count: number }>>(
    '/api/operation-log/cleanup',
    {
      method: 'POST',
      data: params,
    },
  );
}
