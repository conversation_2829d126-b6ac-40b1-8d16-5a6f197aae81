import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { history, useModel, useParams } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Radio,
  Row,
  Space,
  Spin,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import './index.less';

const { TextArea } = Input;

/**
 * 问卷创建/编辑页面
 */
const QuestionnaireCreate: React.FC = () => {
  const {
    questionnaireDetail,
    detailLoading,
    createQuestionnaireAction,
    updateQuestionnaireAction,
    fetchQuestionnaireDetail,
  } = useModel('questionnaire');

  const { initialState } = useModel('@@initialState');

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const params = useParams<{ id: string }>();
  const isEdit = !!params.id;

  // 初始化数据
  useEffect(() => {
    if (isEdit && params.id) {
      fetchQuestionnaireDetail(Number(params.id));
    }
  }, [isEdit, params.id]);

  // 编辑时填充表单数据
  useEffect(() => {
    if (isEdit && questionnaireDetail) {
      form.setFieldsValue({
        title: questionnaireDetail.title,
        description: questionnaireDetail.description,
        month: dayjs(questionnaireDetail.month),
        star_mode: questionnaireDetail.config?.star_mode || 5,
      });
    }
  }, [isEdit, questionnaireDetail, form]);

  // 表单提交
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      // 格式化月份，使用当前用户的学校ID
      const formattedValues: API.ICreateQuestionnaireParams = {
        ...values,
        month: values.month.format('YYYY-MM'),
        sso_school_id: initialState?.userInfo?.enterprise?.id || '',
        config: {
          star_mode: values.star_mode, // 星级模式配置
        },
      };

      let result;
      if (isEdit && params.id) {
        result = await updateQuestionnaireAction(
          Number(params.id),
          formattedValues,
        );
      } else {
        result = await createQuestionnaireAction(formattedValues);
      }

      if (result) {
        // 跳转到问卷列表页
        history.push('/questionnaire/list');
      }
    } catch (error) {
      message.error(isEdit ? '更新问卷失败' : '创建问卷失败');
    } finally {
      setLoading(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    history.push('/questionnaire/list');
  };

  return (
    <div className="questionnaire-create">
      {/* 页面头部 */}
      <Card style={{ marginBottom: 16 }}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              style={{ marginRight: 16 }}
            >
              返回列表
            </Button>
            <h2 style={{ margin: 0 }}>{isEdit ? '编辑问卷' : '创建问卷'}</h2>
          </div>
        </div>
      </Card>

      {/* 表单内容 */}
      <Card>
        <Spin spinning={detailLoading}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              star_mode: 5, // 默认5星制
            }}
          >
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="title"
                  label="问卷标题"
                  rules={[
                    { required: true, message: '请输入问卷标题' },
                    { max: 100, message: '标题长度不能超过100个字符' },
                  ]}
                >
                  <Input placeholder="请输入问卷标题" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="所属学校">
                  <Input
                    value={initialState?.userInfo?.enterprise?.name || ''}
                    disabled
                    placeholder="当前用户学校"
                  />
                  <div className="form-description">
                    问卷将创建在您所属的学校下
                  </div>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="month"
                  label="问卷月份"
                  rules={[{ required: true, message: '请选择问卷月份' }]}
                >
                  <DatePicker.MonthPicker
                    placeholder="请选择月份"
                    format="YYYY-MM"
                    style={{ width: '100%' }}
                    disabledDate={(current) => {
                      // 禁用未来月份
                      return current && current > dayjs().endOf('month');
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="star_mode"
                  label="星级模式"
                  rules={[{ required: true, message: '请选择星级模式' }]}
                >
                  <Radio.Group>
                    <Radio value={5}>5星制</Radio>
                    <Radio value={10}>10星制</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>

            <Row>
              <Col span={24}>
                <Form.Item
                  name="description"
                  label="问卷描述"
                  rules={[{ max: 500, message: '描述长度不能超过500个字符' }]}
                >
                  <TextArea
                    placeholder="请输入问卷描述（可选）"
                    rows={4}
                    showCount
                    maxLength={500}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 提交按钮 */}
            <Form.Item>
              <div style={{ textAlign: 'center', marginTop: 32 }}>
                <Space size="large">
                  <Button size="large" onClick={handleBack}>
                    取消
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    htmlType="submit"
                    loading={loading}
                    icon={<SaveOutlined />}
                  >
                    {isEdit ? '保存修改' : '创建问卷'}
                  </Button>
                </Space>
              </div>
            </Form.Item>
          </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default QuestionnaireCreate;
